{"data_mtime": 1756413934, "dep_lines": [1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "abc", "io", "os", "typing"], "hash": "22a357adbbfd506a1d18a683b154d78217093c63", "id": "student_register", "ignore_all": false, "interface_hash": "43ec35a9b7d57e26c43d3fd86f27c548988cada3", "mtime": 1756413981, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\dev\\GN25060018715\\Level 1 - Foundations of AI Engineering\\M02T05 – IO Operations\\Code Files\\Input\\Task file\\student_register.py", "plugin_data": null, "size": 358, "suppressed": [], "version_id": "1.15.0"}