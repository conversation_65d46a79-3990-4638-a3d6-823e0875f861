{".class": "MypyFile", "_fullname": "student_register", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "student_register.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "student_register.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "student_register.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "student_register.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "student_register.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "student_register.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "file": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "student_register.file", "name": "file", "type": {".class": "Instance", "args": ["_io._W<PERSON><PERSON><PERSON>er"], "extra_attrs": null, "type_ref": "_io.TextIOWrapper"}}}, "i": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_index_var", "is_inferred"], "fullname": "student_register.i", "name": "i", "type": "builtins.int"}}, "student_id": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "student_register.student_id", "name": "student_id", "type": "builtins.str"}}, "student_register": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "student_register.student_register", "name": "student_register", "type": "builtins.int"}}}, "path": "C:\\dev\\GN25060018715\\Level 1 - Foundations of AI Engineering\\M02T05 – IO Operations\\Code Files\\Input\\Task file\\student_register.py"}